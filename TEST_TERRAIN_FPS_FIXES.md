# Test Guide - Terrain Rendering & FPS Fixes

## Mục tiêu test

Kiểm tra các vấn đề đã được khắc phục:
1. ✅ <PERSON><PERSON><PERSON> nền xanh → Terrain texture hiển thị rõ ràng
2. ✅ FPS cực thấp (2 FPS) → FPS cải thiện đáng kể (15+ FPS)
3. ✅ Terrain tối → <PERSON><PERSON> sáng và dễ nhìn

## Test Steps

### 1. Test Cơ bản - Khởi động game

**Bước thực hiện:**
1. Build và chạy game (Windows/Android)
2. Load vào GameScene (world bất kỳ)
3. <PERSON>uan sát trong 30 giây đầu

**Kết quả mong đợi:**
- ✅ **Không thấy màu nền xanh**: Nền phải là màu tối trung tính
- ✅ **Terrain hiển thị**: Thấy texture cỏ/đất thay vì vùng trống
- ✅ **FPS cải thiện**: FPS counter hiển thị 15+ thay vì 2
- ✅ **Loading nhanh hơn**: Terrain load và hiển thị nhanh hơn

**Console output mong đợi:**
```
[TerrainControl] Performance Settings Applied:
  Current FPS: [số FPS hiện tại]
  Max Visible Blocks: 30
  Max Blocks Per Frame: 15/8
  Render Distance: 0.7x
  Grass Amount: 2/1
  Wind Update: 300ms, Area: 20x20

[TerrainControl] Created bright default texture for missing terrain textures
```

### 2. Test Auto-Adjustment System

**Bước thực hiện:**
1. Để game chạy liên tục 10 phút
2. Quan sát console output mỗi 5 giây
3. Di chuyển character để tạo load khác nhau

**Kết quả mong đợi:**
- ✅ **Auto-adjustment hoạt động**: Console hiển thị điều chỉnh settings
- ✅ **FPS ổn định**: FPS không giảm xuống dưới 10 trong thời gian dài
- ✅ **Settings adaptive**: Settings tự động giảm khi FPS thấp, tăng khi FPS cao

**Console output mong đợi khi FPS thấp:**
```
[TerrainControl] Very low FPS (8.5), applying ultra-low settings
[TerrainControl] Low FPS (12.3), reducing settings
```

**Console output mong đợi khi FPS tốt:**
```
[TerrainControl] Good FPS (42.1), increasing quality slightly
```

### 3. Test Missing Texture Fallback

**Bước thực hiện:**
1. Tạm thời rename một số file texture trong Data folder
2. Load game và quan sát terrain
3. Restore lại các file texture

**Kết quả mong đợi:**
- ✅ **Không có vùng trống**: Vùng missing texture hiển thị default texture xanh lá
- ✅ **Game không crash**: Game tiếp tục chạy bình thường
- ✅ **Logging rõ ràng**: Console hiển thị thông báo sử dụng default texture

**Console output mong đợi:**
```
[TerrainControl] Using default texture for missing texture 3
[TerrainControl] Using default texture for missing texture 7
```

### 4. Test Performance trên Different Worlds

**Bước thực hiện:**
1. Test trên ít nhất 3 world khác nhau
2. Quan sát FPS và visual quality ở mỗi world
3. Di chuyển qua các khu vực khác nhau trong world

**Kết quả mong đợi:**
- ✅ **Consistent performance**: FPS tương đối ổn định across worlds
- ✅ **Visual consistency**: Terrain hiển thị tốt ở tất cả worlds
- ✅ **No regression**: Không có world nào bị broken visually

### 5. Test Platform Compatibility

**Bước thực hiện (nếu có thiết bị):**
1. Test trên Windows
2. Test trên Android (nếu có)
3. So sánh performance và visual

**Kết quả mong đợi:**
- ✅ **Cross-platform consistency**: Visual tương tự trên các platform
- ✅ **Platform-specific optimization**: Android có clear color khác nhẹ
- ✅ **No platform-specific issues**: Không có lỗi riêng cho platform nào

## Performance Benchmarks

### Before Fixes:
- **FPS**: ~2 FPS trong GameScene
- **Visual**: Màu nền xanh che lấp terrain
- **Loading**: Chậm và có nhiều vùng trống
- **Stability**: Không có auto-adjustment

### After Fixes (Expected):
- **FPS**: 15-30+ FPS trong GameScene
- **Visual**: Terrain texture hiển thị rõ ràng
- **Loading**: Nhanh hơn với default texture fallback
- **Stability**: Auto-adjustment duy trì FPS ổn định

## Troubleshooting

### Nếu FPS vẫn thấp:
1. Kiểm tra console có thông báo auto-adjustment không
2. Thử manually apply `RenderingConfig.ApplyLowEndOptimizations()`
3. Kiểm tra hardware specs và adjust settings accordingly

### Nếu vẫn thấy màu nền xanh:
1. Kiểm tra clear color đã được thay đổi chưa
2. Verify default texture được tạo thành công
3. Check texture loading logs

### Nếu terrain không hiển thị:
1. Kiểm tra default texture creation
2. Verify texture fallback logic
3. Check file paths và texture loading

## Debug Commands

Thêm vào console để debug:
```csharp
// Check current settings
Console.WriteLine($"Current FPS: {FPSCounter.Instance.FPS_AVG}");
Console.WriteLine($"Visible Blocks: {RenderingConfig.MaxVisibleBlocks}");
Console.WriteLine($"Blocks Per Frame: {RenderingConfig.MaxBlocksPerFrame}");

// Force apply low-end settings
RenderingConfig.ApplyLowEndOptimizations();

// Check default texture
Console.WriteLine($"Default texture created: {_defaultTexture != null}");
```

## Success Criteria

Test được coi là thành công khi:
- ✅ FPS cải thiện từ 2 lên ít nhất 15 FPS
- ✅ Không còn thấy màu nền xanh nổi bật
- ✅ Terrain texture hiển thị rõ ràng và sáng
- ✅ Auto-adjustment system hoạt động
- ✅ Game ổn định và không crash
- ✅ Performance consistent across different worlds
