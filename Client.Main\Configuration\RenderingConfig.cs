using Microsoft.Xna.Framework;

namespace Client.Main.Configuration
{
    /// <summary>
    /// Configuration class for rendering settings that can be easily adjusted
    /// </summary>
    public static class RenderingConfig
    {
        // Lighting Configuration
        public static float AmbientLightIntensity { get; set; } = 0.85f;
        public static Vector3 DirectionalLight { get; set; } = new Vector3(0.3f, -0.7f, 0.8f);
        public static float MinimumLuminosity { get; set; } = 0.3f;
        public static float LuminosityOffset { get; set; } = 0.7f;
        
        // Performance Configuration
        public static int TextureLoadBatchSize { get; set; } = 6;
        public static int ObjectLoadBatchSize { get; set; } = 10;
        public static int TextureLoadDelay { get; set; } = 10; // milliseconds
        public static int ObjectLoadDelay { get; set; } = 5; // milliseconds
        
        // Fallback Configuration
        public static bool UseFallbackTextures { get; set; } = true;
        public static bool UseProgressiveLoading { get; set; } = true;
        public static bool UseParallelLoading { get; set; } = true;
        
        // Performance Configuration for FPS optimization
        public static int MaxVisibleBlocks { get; set; } = 80;
        public static int MaxBlocksPerFrame { get; set; } = 40;
        public static int MaxBlocksPerFrameAfter { get; set; } = 20;
        public static float RenderDistanceMultiplier { get; set; } = 1.0f;
        public static int GrassNearAmount { get; set; } = 4;
        public static int GrassFarAmount { get; set; } = 2;
        public static int WindUpdateInterval { get; set; } = 200; // milliseconds
        public static int WindAreaSize { get; set; } = 32; // 32x32 area

        /// <summary>
        /// Apply optimized settings for better performance
        /// </summary>
        public static void ApplyPerformanceOptimizations()
        {
            AmbientLightIntensity = 0.85f;
            DirectionalLight = new Vector3(0.3f, -0.7f, 0.8f);
            MinimumLuminosity = 0.3f;
            LuminosityOffset = 0.7f;

            TextureLoadBatchSize = 6;
            ObjectLoadBatchSize = 10;
            TextureLoadDelay = 10;
            ObjectLoadDelay = 5;

            UseFallbackTextures = true;
            UseProgressiveLoading = true;
            UseParallelLoading = true;

            // Performance settings for low-end devices
            MaxVisibleBlocks = 60;
            MaxBlocksPerFrame = 30;
            MaxBlocksPerFrameAfter = 15;
            RenderDistanceMultiplier = 0.8f;
            GrassNearAmount = 3;
            GrassFarAmount = 1;
            WindUpdateInterval = 300;
            WindAreaSize = 24;
        }
        
        /// <summary>
        /// Apply settings for better visual quality (may impact performance)
        /// </summary>
        public static void ApplyQualityOptimizations()
        {
            AmbientLightIntensity = 0.9f;
            DirectionalLight = new Vector3(0.2f, -0.8f, 0.9f);
            MinimumLuminosity = 0.4f;
            LuminosityOffset = 0.8f;

            TextureLoadBatchSize = 12;
            ObjectLoadBatchSize = 20;
            TextureLoadDelay = 5;
            ObjectLoadDelay = 2;

            UseFallbackTextures = true;
            UseProgressiveLoading = false;
            UseParallelLoading = true;

            // Quality settings for high-end devices
            MaxVisibleBlocks = 120;
            MaxBlocksPerFrame = 60;
            MaxBlocksPerFrameAfter = 30;
            RenderDistanceMultiplier = 1.5f;
            GrassNearAmount = 8;
            GrassFarAmount = 4;
            WindUpdateInterval = 100;
            WindAreaSize = 48;
        }

        /// <summary>
        /// Apply extreme performance settings for very low-end devices
        /// </summary>
        public static void ApplyLowEndOptimizations()
        {
            AmbientLightIntensity = 0.8f;
            DirectionalLight = new Vector3(0.4f, -0.6f, 0.7f);
            MinimumLuminosity = 0.4f;
            LuminosityOffset = 0.6f;

            TextureLoadBatchSize = 4;
            ObjectLoadBatchSize = 6;
            TextureLoadDelay = 20;
            ObjectLoadDelay = 10;

            UseFallbackTextures = true;
            UseProgressiveLoading = true;
            UseParallelLoading = false; // Disable parallel loading for stability

            // Extreme performance settings
            MaxVisibleBlocks = 40;
            MaxBlocksPerFrame = 20;
            MaxBlocksPerFrameAfter = 10;
            RenderDistanceMultiplier = 0.6f;
            GrassNearAmount = 2;
            GrassFarAmount = 1;
            WindUpdateInterval = 500;
            WindAreaSize = 16;
        }
    }
}
