using Microsoft.Xna.Framework;

namespace Client.Main.Configuration
{
    /// <summary>
    /// Configuration class for rendering settings that can be easily adjusted
    /// </summary>
    public static class RenderingConfig
    {
        // Lighting Configuration
        public static float AmbientLightIntensity { get; set; } = 0.85f;
        public static Vector3 DirectionalLight { get; set; } = new Vector3(0.3f, -0.7f, 0.8f);
        public static float MinimumLuminosity { get; set; } = 0.3f;
        public static float LuminosityOffset { get; set; } = 0.7f;
        
        // Performance Configuration
        public static int TextureLoadBatchSize { get; set; } = 6;
        public static int ObjectLoadBatchSize { get; set; } = 10;
        public static int TextureLoadDelay { get; set; } = 10; // milliseconds
        public static int ObjectLoadDelay { get; set; } = 5; // milliseconds
        
        // Fallback Configuration
        public static bool UseFallbackTextures { get; set; } = true;
        public static bool UseProgressiveLoading { get; set; } = true;
        public static bool UseParallelLoading { get; set; } = true;
        
        // Performance Configuration for FPS optimization - Aggressive settings for low FPS issues
        public static int MaxVisibleBlocks { get; set; } = 30; // Reduced from 80 to 30
        public static int MaxBlocksPerFrame { get; set; } = 15; // Reduced from 40 to 15
        public static int MaxBlocksPerFrameAfter { get; set; } = 8; // Reduced from 20 to 8
        public static float RenderDistanceMultiplier { get; set; } = 0.7f; // Reduced from 1.0f to 0.7f
        public static int GrassNearAmount { get; set; } = 2; // Reduced from 4 to 2
        public static int GrassFarAmount { get; set; } = 1; // Reduced from 2 to 1
        public static int WindUpdateInterval { get; set; } = 300; // Increased from 200 to 300ms
        public static int WindAreaSize { get; set; } = 20; // Reduced from 32 to 20

        /// <summary>
        /// Apply optimized settings for better performance
        /// </summary>
        public static void ApplyPerformanceOptimizations()
        {
            AmbientLightIntensity = 0.85f;
            DirectionalLight = new Vector3(0.3f, -0.7f, 0.8f);
            MinimumLuminosity = 0.3f;
            LuminosityOffset = 0.7f;

            TextureLoadBatchSize = 6;
            ObjectLoadBatchSize = 10;
            TextureLoadDelay = 10;
            ObjectLoadDelay = 5;

            UseFallbackTextures = true;
            UseProgressiveLoading = true;
            UseParallelLoading = true;

            // Performance settings for low-end devices - More aggressive
            MaxVisibleBlocks = 25;
            MaxBlocksPerFrame = 12;
            MaxBlocksPerFrameAfter = 6;
            RenderDistanceMultiplier = 0.6f;
            GrassNearAmount = 2;
            GrassFarAmount = 1;
            WindUpdateInterval = 400;
            WindAreaSize = 16;
        }
        
        /// <summary>
        /// Apply settings for better visual quality (may impact performance)
        /// </summary>
        public static void ApplyQualityOptimizations()
        {
            AmbientLightIntensity = 0.9f;
            DirectionalLight = new Vector3(0.2f, -0.8f, 0.9f);
            MinimumLuminosity = 0.4f;
            LuminosityOffset = 0.8f;

            TextureLoadBatchSize = 12;
            ObjectLoadBatchSize = 20;
            TextureLoadDelay = 5;
            ObjectLoadDelay = 2;

            UseFallbackTextures = true;
            UseProgressiveLoading = false;
            UseParallelLoading = true;

            // Quality settings for high-end devices
            MaxVisibleBlocks = 120;
            MaxBlocksPerFrame = 60;
            MaxBlocksPerFrameAfter = 30;
            RenderDistanceMultiplier = 1.5f;
            GrassNearAmount = 8;
            GrassFarAmount = 4;
            WindUpdateInterval = 100;
            WindAreaSize = 48;
        }

        /// <summary>
        /// Apply extreme performance settings for very low-end devices
        /// </summary>
        public static void ApplyLowEndOptimizations()
        {
            AmbientLightIntensity = 0.8f;
            DirectionalLight = new Vector3(0.4f, -0.6f, 0.7f);
            MinimumLuminosity = 0.4f;
            LuminosityOffset = 0.6f;

            TextureLoadBatchSize = 4;
            ObjectLoadBatchSize = 6;
            TextureLoadDelay = 20;
            ObjectLoadDelay = 10;

            UseFallbackTextures = true;
            UseProgressiveLoading = true;
            UseParallelLoading = false; // Disable parallel loading for stability

            // Extreme performance settings - Ultra low for 2 FPS issues
            MaxVisibleBlocks = 15;
            MaxBlocksPerFrame = 8;
            MaxBlocksPerFrameAfter = 4;
            RenderDistanceMultiplier = 0.5f;
            GrassNearAmount = 1;
            GrassFarAmount = 0; // Disable far grass completely
            WindUpdateInterval = 600;
            WindAreaSize = 12;
        }
    }
}
