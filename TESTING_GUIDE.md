# Hướng dẫn Test Các <PERSON> tiến Performance

## Chuẩn bị Test

### 1. Build Project
```bash
# Build cho Windows
dotnet publish ./MuWin/MuWin.csproj -f net9.0-windows -c Release

# Build cho Android
dotnet publish ./MuAndroid/MuAndroid.csproj -f net9.0-android -c Release -p:AndroidSdkDirectory="C:\Users\<USER>\AppData\Local\Android\Sdk" -p:JavaSdkDirectory="C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" -p:AcceptAndroidSdkLicenses=True
```

### 2. Chuẩn bị Data
- Đảm bảo đã download và extract MU data files
- Cập nhật đường dẫn trong `Client.Main/Constants.cs`

## Test Cases

### A. Test Màu nền và Terrain Rendering

#### Test 1: Kiểm tra màu nền
**<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> nhận không còn vùng xám/đen trên terrain

**Cách test**:
1. Khởi động game
2. Load vào các world khác nhau (World0, World1, World2, etc.)
3. Di chuyển camera xung quanh terrain
4. Quan sát màu nền và terrain

**Kết quả mong đợi**:
- Màu nền là xanh đậm tự nhiên thay vì đen/xám
- Terrain luôn có màu sắc rõ ràng, không có vùng đen/xám
- Default texture (khi texture gốc chưa load) có màu cỏ xanh tự nhiên

#### Test 2: Kiểm tra lighting
**Mục đích**: Đảm bảo terrain luôn visible ngay cả trong vùng tối

**Cách test**:
1. Di chuyển đến các vùng có lighting thấp
2. Kiểm tra các góc terrain khác nhau
3. Test trong điều kiện ánh sáng khác nhau

**Kết quả mong đợi**:
- Terrain luôn visible với minimum brightness
- Không có vùng hoàn toàn đen
- Lighting tự nhiên và mượt mà

### B. Test Loading Performance

#### Test 3: Đo thời gian loading world
**Mục đích**: Xác nhận cải thiện tốc độ loading

**Cách test**:
1. Đo thời gian từ khi bắt đầu load world đến khi hoàn thành
2. Test với nhiều world khác nhau
3. So sánh với version trước (nếu có)

**Kết quả mong đợi**:
- Texture loading nhanh hơn 40-60%
- World transition nhanh hơn 50-70%
- Priority textures (0-7) load trước và nhanh chóng

#### Test 4: Kiểm tra texture loading progress
**Mục đích**: Xác nhận texture loading được tối ưu

**Cách test**:
1. Quan sát console output khi loading
2. Kiểm tra priority textures load trước
3. Theo dõi background loading progress

**Kết quả mong đợi**:
- Console hiển thị "[TerrainControl] Loaded priority texture X"
- Priority textures (0-7) load hoàn thành trước
- Background textures load song song không block UI

### C. Test Rendering Performance

#### Test 5: Đo FPS performance
**Mục đích**: Xác nhận cải thiện rendering performance

**Cách test**:
1. Enable FPS counter trong game
2. Di chuyển camera trong các tình huống khác nhau:
   - Terrain với nhiều grass
   - Vùng có nhiều objects
   - Zoom in/out
3. Ghi lại FPS trung bình

**Kết quả mong đợi**:
- FPS cải thiện 15-25%
- Ít frame drops khi di chuyển nhanh
- Stable performance khi zoom

#### Test 6: Test grass rendering
**Mục đích**: Kiểm tra grass optimization

**Cách test**:
1. Di chuyển đến vùng có nhiều grass
2. Test ở các khoảng cách khác nhau
3. Quan sát grass density và wind animation

**Kết quả mong đợi**:
- Grass density giảm nhẹ nhưng vẫn đẹp mắt
- Wind animation mượt mà
- Performance tốt hơn trong vùng grass dày

### D. Test Memory và Stability

#### Test 7: Test world switching
**Mục đích**: Kiểm tra memory management

**Cách test**:
1. Chuyển đổi giữa các world liên tục (10-20 lần)
2. Monitor memory usage
3. Kiểm tra có memory leaks không

**Kết quả mong đợi**:
- Không có memory leaks
- Memory usage stable sau GC
- Không crash khi switch world nhiều lần

#### Test 8: Test error handling
**Mục đích**: Kiểm tra stability khi có lỗi

**Cách test**:
1. Test với missing texture files
2. Test với corrupted data
3. Test timeout scenarios

**Kết quả mong đợi**:
- Game không crash khi thiếu files
- Fallback textures hoạt động đúng
- Error messages rõ ràng trong console

## Benchmark Numbers

### Trước optimization (baseline):
- World loading: ~15-25 giây
- Texture loading: Sequential, blocking UI
- FPS: Baseline (ghi lại số cụ thể)
- Memory: Có leaks khi switch world

### Sau optimization (target):
- World loading: ~8-15 giây (40-60% improvement)
- Texture loading: Priority + parallel, non-blocking
- FPS: +15-25% improvement
- Memory: Stable, no leaks

## Tools để Monitor

1. **FPS Counter**: Built-in game FPS display
2. **Console Output**: Texture loading progress
3. **Task Manager**: Memory usage monitoring
4. **Visual Studio Diagnostic Tools**: Detailed profiling

## Troubleshooting

### Nếu vẫn thấy vùng xám/đen:
1. Kiểm tra texture files có tồn tại không
2. Xem console có error messages không
3. Verify default texture được tạo đúng

### Nếu loading vẫn chậm:
1. Kiểm tra disk I/O performance
2. Verify concurrent loading hoạt động
3. Check network nếu loading từ remote

### Nếu FPS không cải thiện:
1. Kiểm tra GPU utilization
2. Verify culling optimization hoạt động
3. Monitor draw calls

## Ghi chú

- Test trên hardware khác nhau để đảm bảo compatibility
- Ghi lại metrics cụ thể để so sánh
- Test cả Debug và Release builds
- Kiểm tra trên cả Windows và Android nếu có thể
